ℹ️    Loaded backtest_ZYDUSLIFE_3min.parquet: 36 rows
ℹ️  Loading data from: data/backtest/backtest_ZYDUSLIFE_5min.parquet
ℹ️    Loaded backtest_ZYDUSLIFE_5min.parquet: 36 rows
ℹ️  Combined dataset: 31104 rows, 12 columns
ℹ️  Loaded 31104 samples for training
ℹ️  Preprocessing data with Polars...
ℹ️  Creating concrete meta-learning targets...
ℹ️  Preprocessed: 11 features, 1 targets
🔄 Training models for task: roi_prediction
ℹ️  [GPU] CUDA available with 1 device(s)
ℹ️  [GPU] LightGBM GPU parameters configured
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
1 warning generated.
  ✓   lightgbm - Score: 1.0000
[ERROR] Failed to train xgboost for roi_prediction: XGBModel.fit() got an unexpected keyword argument 'early_stopping_rounds'
  ✓   catboost - Score: 1.0000
ℹ️  [GPU] TabNet GPU parameters configured
epoch 0  | loss: 50282.35072| val_0_mse: 48284.31641|  0:00:00s
epoch 1  | loss: 44630.469| val_0_mse: 37996.39453|  0:00:01s
epoch 2  | loss: 35418.69026| val_0_mse: 25629.08789|  0:00:01s
epoch 3  | loss: 23056.05798| val_0_mse: 10749.11035|  0:00:01s
epoch 4  | loss: 10547.12626| val_0_mse: 4326.76025|  0:00:02s
epoch 5  | loss: 2139.56308| val_0_mse: 603.22589|  0:00:02s
epoch 6  | loss: 326.82946| val_0_mse: 2045.07959|  0:00:03s
epoch 7  | loss: 310.32521| val_0_mse: 79.40855|  0:00:03s
epoch 8  | loss: 194.42005| val_0_mse: 129.75522|  0:00:04s
epoch 9  | loss: 276.10375| val_0_mse: 64.7861 |  0:00:04s
epoch 10 | loss: 239.80751| val_0_mse: 31.26435|  0:00:04s
epoch 11 | loss: 189.79505| val_0_mse: 29.19542|  0:00:05s
epoch 12 | loss: 152.12458| val_0_mse: 32.70345|  0:00:05s
epoch 13 | loss: 222.04631| val_0_mse: 303.27057|  0:00:06s
epoch 14 | loss: 168.06576| val_0_mse: 12.03078|  0:00:06s
epoch 15 | loss: 127.74032| val_0_mse: 52.9403 |  0:00:06s
epoch 16 | loss: 198.70913| val_0_mse: 79.74142|  0:00:07s
epoch 17 | loss: 121.92853| val_0_mse: 67.23192|  0:00:07s
epoch 18 | loss: 105.24584| val_0_mse: 46.66842|  0:00:08s
epoch 19 | loss: 106.07581| val_0_mse: 156.35442|  0:00:08s
epoch 20 | loss: 84.37198| val_0_mse: 5.51036 |  0:00:08s
epoch 21 | loss: 115.57812| val_0_mse: 126.35487|  0:00:09s
epoch 22 | loss: 78.0197 | val_0_mse: 35.27443|  0:00:09s
epoch 23 | loss: 92.3296 | val_0_mse: 62.84108|  0:00:10s
epoch 24 | loss: 113.0376| val_0_mse: 33.16288|  0:00:10s
epoch 25 | loss: 104.44392| val_0_mse: 80.51572|  0:00:10s
epoch 26 | loss: 136.50035| val_0_mse: 18.44348|  0:00:11s
epoch 27 | loss: 79.98067| val_0_mse: 65.40094|  0:00:11s
epoch 28 | loss: 85.40675| val_0_mse: 16.19645|  0:00:12s
epoch 29 | loss: 83.63229| val_0_mse: 72.19364|  0:00:12s
epoch 30 | loss: 49.69586| val_0_mse: 14.73401|  0:00:12s
epoch 31 | loss: 48.71531| val_0_mse: 59.37708|  0:00:13s
epoch 32 | loss: 72.35513| val_0_mse: 16.55595|  0:00:13s
epoch 33 | loss: 94.63871| val_0_mse: 19.101  |  0:00:14s
epoch 34 | loss: 60.22137| val_0_mse: 11.94776|  0:00:14s
epoch 35 | loss: 101.09385| val_0_mse: 53.95272|  0:00:14s

Early stopping occurred at epoch 35 with best_epoch = 20 and best_val_0_mse = 5.51036
  ✓   tabnet - Score: 0.9997
  ✓   mlp - Score: 1.0000
  ✓   sgd - Score: 1.0000
[ERROR] Failed to create ensemble for roi_prediction: For early stopping, at least one dataset and eval metric is required for evaluation
ℹ️  Saving models as: enhanced_ai_ensemble
ℹ️  Saved batch model: roi_prediction/lightgbm
ℹ️  Saved batch model: roi_prediction/catboost
ℹ️  Saved batch model: roi_prediction/tabnet
ℹ️  Saved batch model: roi_prediction/mlp
ℹ️  Saved batch model: roi_prediction/sgd
ℹ️  Saved scalers
ℹ️  Saved feature_selectors
ℹ️  Saved imputers
ℹ️  Models and metadata saved successfully to: data/models/enhanced/enhanced_ai_ensemble
ℹ️  Enhanced AI training completed successfully!
✅ [SUCCESS] Enhanced AI training completed - 1 tasks trained
✅ [SUCCESS] ai_training agent completed successfully
[SUCCESS] ai_training agent completed successfully