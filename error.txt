2025-08-23 12:08:05,545 - agents.ai_training.main_agent - INFO - Loaded 31104 samples for training
2025-08-23 12:08:05,545 - agents.ai_training.data_handler - INFO - Preprocessing data with Polars...
2025-08-23 12:08:06,936 - agents.ai_training.data_handler - INFO - Creating concrete meta-learning targets...
2025-08-23 12:08:06,953 - agents.ai_training.data_handler - INFO - Preprocessed: 11 features, 1 targets
2025-08-23 12:08:06,955 - agents.ai_training.main_agent - INFO - Training models for task: roi_prediction
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.028380 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 1530
[LightGBM] [Info] Number of data points in the train set: 14282, number of used features: 6
[LightGBM] [Info] Start training from score 183.001099
Training until validation scores don't improve for 100 rounds
Did not meet early stopping. Best iteration is:
[100]   valid_0's l2: 0.327966
2025-08-23 12:08:07,098 - agents.ai_training.main_agent - INFO -   lightgbm - Score: 1.0000
2025-08-23 12:08:07,524 - agents.ai_training.main_agent - INFO -   xgboost - Score: 1.0000
2025-08-23 12:08:09,753 - agents.ai_training.main_agent - INFO -   catboost - Score: 1.0000
2025-08-23 12:08:09,753 - agents.ai_training.main_agent - ERROR - Failed to train tabnet for roi_prediction: TabModel.__init__() got an unexpected keyword argument 'random_state'
2025-08-23 12:08:12,596 - agents.ai_training.main_agent - INFO -   mlp - Score: 1.0000
2025-08-23 12:08:12,628 - agents.ai_training.main_agent - INFO -   sgd - Score: 1.0000
/media/jmk/BKP/Documents/Option/.venv/lib/python3.12/site-packages/cupy/_environment.py:596: UserWarning: 
--------------------------------------------------------------------------------

  CuPy may not function correctly because multiple CuPy packages are installed
  in your environment:

    cupy-cuda11x, cupy-cuda12x

  Follow these steps to resolve this issue:

    1. For all packages listed above, run the following command to remove all
       existing CuPy installations:

         $ pip uninstall <package_name>

      If you previously installed CuPy via conda, also run the following:

         $ conda uninstall cupy

    2. Install the appropriate CuPy package.
       Refer to the Installation Guide for detailed instructions.

         https://docs.cupy.dev/en/stable/install.html

--------------------------------------------------------------------------------

  warnings.warn(f'''
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.000251 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 1530
[LightGBM] [Info] Number of data points in the train set: 14282, number of used features: 6
[LightGBM] [Info] Start training from score 183.001099
/media/jmk/BKP/Documents/Option/.venv/lib/python3.12/site-packages/cupy/_environment.py:596: UserWarning: 
--------------------------------------------------------------------------------

  CuPy may not function correctly because multiple CuPy packages are installed
  in your environment:

    cupy-cuda11x, cupy-cuda12x

  Follow these steps to resolve this issue:

    1. For all packages listed above, run the following command to remove all
       existing CuPy installations:

         $ pip uninstall <package_name>

      If you previously installed CuPy via conda, also run the following:

         $ conda uninstall cupy

    2. Install the appropriate CuPy package.
       Refer to the Installation Guide for detailed instructions.

         https://docs.cupy.dev/en/stable/install.html

--------------------------------------------------------------------------------

  warnings.warn(f'''
[LightGBM] [Info] Auto-choosing row-wise multi-threading, the overhead of testing was 0.000522 seconds.
You can set `force_row_wise=true` to remove the overhead.
And if memory is not enough, you can set `force_col_wise=true`.
[LightGBM] [Info] Total Bins 1530
[LightGBM] [Info] Number of data points in the train set: 9522, number of used features: 6
[LightGBM] [Info] Start training from score 183.042334
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.000949 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 1530
[LightGBM] [Info] Number of data points in the train set: 9521, number of used features: 6
[LightGBM] [Info] Start training from score 183.194675
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.001082 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 1530
[LightGBM] [Info] Number of data points in the train set: 9521, number of used features: 6
[LightGBM] [Info] Start training from score 182.766285
2025-08-23 12:08:35,604 - agents.ai_training.main_agent - INFO -   Ensemble - Score: 1.0000