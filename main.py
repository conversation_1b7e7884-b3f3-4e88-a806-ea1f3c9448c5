#!/usr/bin/env python3
"""
[INIT] CENTRALIZED MAIN ENTRY POINT FOR INTRADAY AI TRADING SYSTEM
═══════════════════════════════════════════════════════════════════════════════

This is the unified entry point for all trading system agents and workflows.
Supports both individual agent execution and complete workflow orchestration.

Features:
[TARGET] Individual agent execution with custom configurations
[WORKFLOW] Complete workflow orchestration with dependency management
[FAST] GPU optimization and performance monitoring
[STATUS] Real-time status monitoring and health checks
[SECURITY] Error handling and graceful shutdown
[METRICS] Performance metrics and logging

Usage Examples:
  # Individual Agents
  python main.py --agent signal_generation
  python main.py --agent ai_training --config custom_config.yaml
  python main.py --agent backtesting
  
  # Complete Workflows
  python main.py --workflow full_pipeline
  python main.py --workflow training_pipeline
  python main.py --workflow live_trading
  
  # System Management
  python main.py --health_check
  python main.py --status
  python main.py --optimize_gpu

Author: AI Trading System
Version: 2.0.0 (2024-2025 Optimized)
"""

import os
import sys
import asyncio
import argparse
import logging
import signal
import yaml
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.append(str(PROJECT_ROOT))

# Import utilities
from utils.config_loader import ConfigurationLoader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/main.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Suppress Numba CUDA driver informational messages
logging.getLogger('numba.cuda.cudadrv.driver').setLevel(logging.WARNING)

class TradingSystemOrchestrator:
    """
    Main orchestrator for the trading system
    Manages agent lifecycle, workflows, and system health
    """

    def __init__(self, config_loader: ConfigurationLoader):
        """Initialize the orchestrator"""
        self.config_loader = config_loader
        self.running_agents = {}
        self.shutdown_event = asyncio.Event()
        self.start_time = datetime.now()

        # Trading mode configuration
        self.trading_mode = self._get_trading_mode()
        self.paper_trading_enabled = self.trading_mode == "paper"

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        logger.info(f"[INIT] Trading System Orchestrator initialized - Mode: {self.trading_mode.upper()}")

    def _get_trading_mode(self) -> str:
        """Get trading mode from environment variables"""
        trading_mode = os.getenv('TRADING_MODE', 'paper').lower()

        # Validate trading mode
        valid_modes = ['paper', 'real']
        if trading_mode not in valid_modes:
            logger.error(f"[ERROR] Invalid TRADING_MODE '{trading_mode}'. Must be 'paper' or 'real'.")
            raise ValueError(f"Invalid TRADING_MODE: {trading_mode}")

        # Strict check for PAPER_TRADING_ENABLED in real mode
        paper_trading_enabled_env = os.getenv('PAPER_TRADING_ENABLED')
        if trading_mode == 'real' and paper_trading_enabled_env and paper_trading_enabled_env.lower() == 'true':
            logger.error("[ERROR] TRADING_MODE is 'real', but PAPER_TRADING_ENABLED is 'true'. This is not allowed. "
                         "Set PAPER_TRADING_ENABLED to 'false' or remove it for real trading.")
            raise ValueError("Conflicting trading mode configuration: Real trading with paper trading enabled.")

        return trading_mode
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"[SIGNAL] Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()

    def get_trading_config(self) -> Dict[str, Any]:
        """Get trading configuration based on current mode"""
        base_config = {
            'trading_mode': self.trading_mode,
            'paper_trading_enabled': self.paper_trading_enabled,
            'timestamp': datetime.now().isoformat()
        }

        if self.trading_mode == 'paper':
            base_config.update({
                'paper_trading': {
                    'initial_balance': float(os.getenv('PAPER_TRADING_INITIAL_BALANCE', 100000)),
                    'max_trades_per_day': int(os.getenv('PAPER_TRADING_MAX_TRADES_PER_DAY', 5)),
                    'commission_rate': float(os.getenv('PAPER_TRADING_COMMISSION_RATE', 0.0003)),
                    'max_position_size': float(os.getenv('PAPER_TRADING_MAX_POSITION_SIZE', 20000)),
                    'max_daily_loss': float(os.getenv('PAPER_TRADING_MAX_DAILY_LOSS', 5000)),
                    'margin_multiplier': float(os.getenv('PAPER_TRADING_MARGIN_MULTIPLIER', 3.5))
                }
            })
        else:
            smartapi_config = {
                'api_key': os.getenv('SMARTAPI_API_KEY'),
                'username': os.getenv('SMARTAPI_USERNAME'),
                'password': os.getenv('SMARTAPI_PASSWORD'),
                'totp_token': os.getenv('SMARTAPI_TOTP_TOKEN')
            }

            # Ensure all SmartAPI credentials are provided for real trading
            missing_credentials = [key for key, value in smartapi_config.items() if not value]
            if missing_credentials:
                logger.error(f"[ERROR] Missing SmartAPI credentials for real trading: {', '.join(missing_credentials)}. No fallback.")
                raise ValueError(f"Missing SmartAPI credentials: {', '.join(missing_credentials)}")

            base_config.update({'real_trading': smartapi_config})

        return base_config
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [TARGET] INDIVIDUAL AGENT EXECUTION
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def run_agent(self, agent_name: str, config_path: Optional[str] = None, **kwargs) -> bool:
        """Run a specific agent"""
        try:
            logger.info(f"[START] Starting {agent_name} agent in {self.trading_mode.upper()} mode...")

            # Agent mapping with their runners
            agent_runners = {
                'data_ingestion': self._run_data_ingestion,
                'feature_engineering': self._run_feature_engineering,
                'strategy_generation': self._run_strategy_generation,
                'backtesting': self._run_backtesting,
                'ai_training': self._run_ai_training,
                'market_monitoring': self._run_market_monitoring,
                'signal_generation': self._run_signal_generation,
                'risk_management': self._run_risk_management,
                'execution': self._run_execution,
                'performance_analysis': self._run_performance_analysis,
                'llm_interface': self._run_llm_interface,
                'strategy_evolution': self._run_strategy_evolution
            }

            if agent_name not in agent_runners:
                logger.error(f"[ERROR] Unknown agent: {agent_name}")
                return False

            # Add trading mode to kwargs
            kwargs['trading_mode'] = self.trading_mode
            kwargs['trading_config'] = self.get_trading_config()

            # Run the specific agent
            success = await agent_runners[agent_name](config_path, **kwargs)

            if success:
                logger.info(f"[SUCCESS] {agent_name} agent completed successfully")
            else:
                logger.error(f"[ERROR] {agent_name} agent failed")

            return success

        except Exception as e:
            logger.error(f"[ERROR] Error running {agent_name} agent: {e}")
            return False
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [WORKFLOW] WORKFLOW ORCHESTRATION
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def run_workflow(self, workflow_name: str, **kwargs) -> bool:
        """Run a complete workflow"""
        try:
            logger.info(f"[WORKFLOW] Starting {workflow_name} workflow...")
            
            workflows = {
                'full_pipeline': self._workflow_full_pipeline,
                'training_pipeline': self._workflow_training_pipeline,
                'live_trading': self._workflow_live_trading,
                'data_pipeline': self._workflow_data_pipeline,
                'strategy_development': self._workflow_strategy_development
            }
            
            if workflow_name not in workflows:
                logger.error(f"[ERROR] Unknown workflow: {workflow_name}")
                return False
            
            success = await workflows[workflow_name](**kwargs)
            
            if success:
                logger.info(f"[SUCCESS] {workflow_name} workflow completed successfully")
            else:
                logger.error(f"[ERROR] {workflow_name} workflow failed")
            
            return success
            
        except Exception as e:
            logger.error(f"[ERROR] Error running {workflow_name} workflow: {e}")
            return False
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [TOOLS] AGENT IMPLEMENTATIONS
    # ═══════════════════════════════════════════════════════════════════════════════
    
    async def _run_data_ingestion(self, config_path: Optional[str], **kwargs) -> bool:
        """Run data ingestion agent"""
        try:
            # Import and run data ingestion
            from scripts.historical_data_downloader import main as download_main
            await download_main()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Data ingestion failed: {e}")
            return False
    
    async def _run_feature_engineering(self, config_path: Optional[str], **kwargs) -> bool:
        """Run feature engineering agent"""
        try:
            from agents.run_feature_engineering import main as fe_main
            await fe_main()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Feature engineering failed: {e}")
            return False
    
    async def _run_strategy_generation(self, config_path: Optional[str], **kwargs) -> bool:
        """Run strategy generation agent"""
        try:
            # Strategy generation is handled by strategy evolution agent
            return await self._run_strategy_evolution(config_path, **kwargs)
        except Exception as e:
            logger.error(f"[ERROR] Strategy generation failed: {e}")
            return False
    
    async def _run_backtesting(self, config_path: Optional[str], **kwargs) -> bool:
        """Run backtesting agent"""
        try:
            # Import and run the backtesting module directly
            from agents.enhanced_backtesting_kimi import main_async
            
            # Call the async function directly for full backtesting
            await main_async()
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Backtesting failed: {e}")
            return False
    
    async def _run_ai_training(self, config_path: Optional[str], **kwargs) -> bool:
        """Run AI training agent with fast startup"""
        try:
            # Try new modular agent first with fast startup
            try:
                from agents.ai_training import EnhancedAITrainingAgent, EnhancedAITrainingConfig
                
                # Load custom config if provided
                if config_path and Path(config_path).exists():
                    with open(config_path, 'r') as f:
                        config_data = yaml.safe_load(f)
                    config = EnhancedAITrainingConfig(**config_data)
                else:
                    config = EnhancedAITrainingConfig()
                
                # Initialize enhanced agent with fast startup
                agent = EnhancedAITrainingAgent(config, fast_startup=True)
                
                # Extract symbol and timeframe from kwargs if provided
                symbol = kwargs.get('symbol')
                timeframe = kwargs.get('timeframe')
                
                results = await agent.train_enhanced_models(
                    file_path=config_path, 
                    symbol=symbol, 
                    timeframe=timeframe
                )
                
                if results.get('status') == 'success':
                    logger.info(f"[SUCCESS] Enhanced AI training completed - {results.get('tasks_trained', 0)} tasks trained")
                    return True
                else:
                    logger.error(f"[ERROR] Enhanced AI training failed: {results.get('error', 'Unknown error')}")
                    return False
                    
            except ImportError:
                # Fallback to legacy agent
                logger.info("[FALLBACK] Using legacy AI training agent")
                from agents.run_ai_training import main as ai_main
                sys.argv = ['run_ai_training.py']
                if config_path:
                    sys.argv.extend(['--config', config_path])
                
                await ai_main()
                return True
                
        except Exception as e:
            logger.error(f"[ERROR] AI training failed: {e}")
            return False
    
    async def _run_market_monitoring(self, config_path: Optional[str], **kwargs) -> bool:
        """Run market monitoring agent"""
        try:
            from agents.run_market_monitoring import MarketMonitoringRunner
            config = config_path or "config/market_monitoring_config.yaml"
            runner = MarketMonitoringRunner(config)
            await runner.start()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Market monitoring failed: {e}")
            return False
    
    async def _run_signal_generation(self, config_path: Optional[str], **kwargs) -> bool:
        """Run signal generation agent"""
        try:
            from agents.signal_generation_agent import SignalGenerationAgent
            config = config_path or "config/signal_generation_config.yaml"
            agent = SignalGenerationAgent(config)
            await agent.start()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Signal generation failed: {e}")
            return False
    
    async def _run_risk_management(self, config_path: Optional[str], **kwargs) -> bool:
        """Run risk management agent"""
        try:
            from agents.risk_agent import RiskManagementAgent
            config = config_path or "config/risk_management_config.yaml"
            agent = RiskManagementAgent(config)
            await agent.setup()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Risk management failed: {e}")
            return False
    
    async def _run_execution(self, config_path: Optional[str], **kwargs) -> bool:
        """Run execution agent"""
        try:
            from agents.run_execution_agent import ExecutionAgentRunner
            config = config_path or "config/execution_config.yaml"
            runner = ExecutionAgentRunner(config)

            # Initialize the runner first
            if not await runner.initialize():
                logger.error("[ERROR] Failed to initialize execution agent runner")
                return False

            await runner.run()

            return True
        except Exception as e:
            logger.error(f"[ERROR] Execution agent failed: {e}")
            return False
    
    async def _run_performance_analysis(self, config_path: Optional[str], **kwargs) -> bool:
        """Run performance analysis agent"""
        try:
            from agents.run_performance_analysis import PerformanceAnalysisRunner
            config = config_path or "config/performance_analysis_config.yaml"
            runner = PerformanceAnalysisRunner(config)

            # Initialize the runner first
            if not await runner.initialize():
                logger.error("[ERROR] Failed to initialize performance analysis runner")
                return False

            # Run the performance analysis
            await runner.run()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Performance analysis failed: {e}")
            return False
    
    async def _run_llm_interface(self, config_path: Optional[str], **kwargs) -> bool:
        """Run LLM interface agent"""
        try:
            from agents.run_llm_interface_demo import main as llm_main
            await llm_main()
            return True
        except Exception as e:
            logger.error(f"[ERROR] LLM interface failed: {e}")
            return False
    
    async def _run_strategy_evolution(self, config_path: Optional[str], **kwargs) -> bool:
        """Run strategy evolution agent"""
        try:
            from agents.run_strategy_evolution import StrategyEvolutionRunner
            config = config_path or "config/strategy_evolution_config.yaml"
            runner = StrategyEvolutionRunner(config)
            await runner.start()
            return True
        except Exception as e:
            logger.error(f"[ERROR] Strategy evolution failed: {e}")
            return False

    # ═══════════════════════════════════════════════════════════════════════════════
    # [WORKFLOW] WORKFLOW IMPLEMENTATIONS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _workflow_full_pipeline(self, **kwargs) -> bool:
        """Complete end-to-end trading pipeline"""
        try:
            logger.info("[WORKFLOW] Starting full pipeline workflow...")

            # Step 1: Data Ingestion
            if not await self.run_agent('data_ingestion'):
                return False

            # Step 2: Feature Engineering
            if not await self.run_agent('feature_engineering'):
                return False

            # Step 3: Strategy Generation
            if not await self.run_agent('strategy_generation'):
                return False

            # Step 4: Backtesting
            if not await self.run_agent('backtesting'):
                return False

            # Step 5: AI Training
            if not await self.run_agent('ai_training'):
                return False

            # Step 6: Start live trading agents
            await asyncio.gather(
                self.run_agent('market_monitoring'),
                self.run_agent('signal_generation'),
                self.run_agent('risk_management'),
                self.run_agent('execution'),
                self.run_agent('performance_analysis')
            )

            return True

        except Exception as e:
            logger.error(f"[ERROR] Full pipeline workflow failed: {e}")
            return False

    async def _workflow_training_pipeline(self, **kwargs) -> bool:
        """Training and strategy development pipeline"""
        try:
            logger.info("[WORKFLOW] Starting training pipeline workflow...")

            # Data preparation
            if not await self.run_agent('data_ingestion'):
                return False

            if not await self.run_agent('feature_engineering'):
                return False

            # Strategy development
            if not await self.run_agent('strategy_generation'):
                return False

            if not await self.run_agent('backtesting'):
                return False

            # AI training
            if not await self.run_agent('ai_training'):
                return False

            # Strategy evolution
            if not await self.run_agent('strategy_evolution'):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Training pipeline workflow failed: {e}")
            return False

    async def _workflow_live_trading(self, **kwargs) -> bool:
        """Live trading workflow"""
        try:
            logger.info("[WORKFLOW] Starting live trading workflow...")

            # Start all live trading agents concurrently
            tasks = [
                self.run_agent('market_monitoring'),
                self.run_agent('signal_generation'),
                self.run_agent('risk_management'),
                self.run_agent('execution'),
                self.run_agent('performance_analysis')
            ]

            # Wait for all agents to start
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check if any agent failed
            for i, result in enumerate(results):
                if isinstance(result, Exception) or not result:
                    logger.error(f"[ERROR] Live trading agent {i} failed: {result}")
                    return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Live trading workflow failed: {e}")
            return False

    async def _workflow_data_pipeline(self, **kwargs) -> bool:
        """Data processing pipeline"""
        try:
            logger.info("[WORKFLOW] Starting data pipeline workflow...")

            if not await self.run_agent('data_ingestion'):
                return False

            if not await self.run_agent('feature_engineering'):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Data pipeline workflow failed: {e}")
            return False

    async def _workflow_strategy_development(self, **kwargs) -> bool:
        """Strategy development and optimization workflow"""
        try:
            logger.info("[WORKFLOW] Starting strategy development workflow...")

            if not await self.run_agent('strategy_generation'):
                return False

            if not await self.run_agent('backtesting'):
                return False

            if not await self.run_agent('strategy_evolution'):
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Strategy development workflow failed: {e}")
            return False

    # ═══════════════════════════════════════════════════════════════════════════════
    # [SECURITY] SYSTEM MANAGEMENT & HEALTH CHECKS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive system health check"""
        try:
            logger.info("[HEALTH] Running system health check...")

            health_status = {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'healthy',
                'components': {},
                'system_info': {},
                'recommendations': []
            }

            # Check system resources
            try:
                import psutil
                health_status['system_info'] = {
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'memory_percent': psutil.virtual_memory().percent,
                    'disk_percent': psutil.disk_usage('/').percent,
                    'uptime_hours': (datetime.now() - self.start_time).total_seconds() / 3600
                }
            except ImportError:
                health_status['system_info']['note'] = 'psutil not available'

            # Check GPU availability
            try:
                import torch
                health_status['components']['gpu'] = {
                    'available': torch.cuda.is_available(),
                    'device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0
                }
                if torch.cuda.is_available():
                    health_status['components']['gpu']['memory_allocated'] = torch.cuda.memory_allocated()
                    health_status['components']['gpu']['memory_reserved'] = torch.cuda.memory_reserved()
            except ImportError:
                health_status['components']['gpu'] = {'status': 'torch not available'}

            # Check data directories
            data_dirs = ['data/features', 'data/backtest', 'data/models', 'logs']
            for dir_path in data_dirs:
                path = Path(dir_path)
                health_status['components'][f'dir_{dir_path.replace("/", "_")}'] = {
                    'exists': path.exists(),
                    'writable': path.exists() and os.access(path, os.W_OK)
                }

            # Check configuration files
            config_files = [
                'config/ai_training_config.yaml',
                'config/market_monitoring_config.yaml',
                'config/signal_generation_config.yaml',
                'config/execution_config.yaml'
            ]
            for config_file in config_files:
                path = Path(config_file)
                health_status['components'][f'config_{path.stem}'] = {
                    'exists': path.exists(),
                    'readable': path.exists() and os.access(path, os.R_OK)
                }

            # Generate recommendations
            if health_status['system_info'].get('memory_percent', 0) > 85:
                health_status['recommendations'].append('High memory usage detected - consider reducing chunk sizes')

            if not health_status['components'].get('gpu', {}).get('available', False):
                health_status['recommendations'].append('GPU not available - using CPU-only mode')

            logger.info("[SUCCESS] Health check completed")
            return health_status

        except Exception as e:
            logger.error(f"[ERROR] Health check failed: {e}")
            return {'status': 'error', 'message': str(e)}

    async def optimize_gpu(self) -> bool:
        """Optimize GPU settings for better performance"""
        try:
            logger.info("[CONFIG] Optimizing GPU settings...")

            # Check if GPU is available
            try:
                import torch
                if not torch.cuda.is_available():
                    logger.warning("[WARN] GPU not available, skipping optimization")
                    return False

                # Enable optimizations
                torch.backends.cudnn.benchmark = True
                torch.backends.cudnn.deterministic = False

                # Clear GPU cache
                torch.cuda.empty_cache()

                # Set memory fraction for RTX 3060Ti (8GB)
                torch.cuda.set_per_process_memory_fraction(0.8)

                logger.info("[SUCCESS] GPU optimization completed")
                return True

            except ImportError:
                logger.warning("[WARN] PyTorch not available, skipping GPU optimization")
                return False

        except Exception as e:
            logger.error(f"[ERROR] GPU optimization failed: {e}")
            return False

    async def get_status(self) -> Dict[str, Any]:
        """Get current system status"""
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'uptime_hours': (datetime.now() - self.start_time).total_seconds() / 3600,
                'running_agents': list(self.running_agents.keys()),
                'agent_count': len(self.running_agents)
            }

            return status

        except Exception as e:
            logger.error(f"[ERROR] Status check failed: {e}")
            return {'status': 'error', 'message': str(e)}


# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN FUNCTION & ARGUMENT PARSING
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main entry point for the trading system"""
    parser = argparse.ArgumentParser(
        description='[INIT] Intraday AI Trading System - Centralized Control Center',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
[TARGET] INDIVIDUAL AGENT EXAMPLES:
  python main.py --agent data_ingestion
  python main.py --agent feature_engineering
  python main.py --agent ai_training --config custom_config.yaml
  python main.py --agent backtesting
  python main.py --agent signal_generation
  python main.py --agent market_monitoring
  python main.py --agent risk_management
  python main.py --agent execution
  python main.py --agent performance_analysis
  python main.py --agent llm_interface
  python main.py --agent strategy_evolution

[WORKFLOW] WORKFLOW EXAMPLES:
  python main.py --workflow full_pipeline
  python main.py --workflow training_pipeline
  python main.py --workflow live_trading
  python main.py --workflow data_pipeline
  python main.py --workflow strategy_development

[SECURITY] SYSTEM MANAGEMENT:
  python main.py --health_check
  python main.py --status
  python main.py --optimize_gpu

[STATUS] MONITORING:
  python main.py --agent market_monitoring --config config/market_monitoring_config.yaml
  python main.py --workflow live_trading --monitor
        """
    )

    # Agent execution
    parser.add_argument(
        '--agent',
        choices=[
            'data_ingestion', 'feature_engineering', 'strategy_generation',
            'backtesting', 'ai_training', 'market_monitoring', 'signal_generation',
            'risk_management', 'execution', 'performance_analysis',
            'llm_interface', 'strategy_evolution'
        ],
        help='Run a specific agent'
    )

    # Workflow execution
    parser.add_argument(
        '--workflow',
        choices=[
            'full_pipeline', 'training_pipeline', 'live_trading',
            'data_pipeline', 'strategy_development'
        ],
        help='Run a complete workflow'
    )

    # Configuration
    parser.add_argument(
        '--config',
        type=str,
        help='Path to custom configuration file'
    )

    # Trading mode
    parser.add_argument(
        '--trading-mode',
        choices=['paper', 'real'],
        help='Override trading mode (paper/real) from environment'
    )

    # System management
    parser.add_argument(
        '--health_check',
        action='store_true',
        help='Run comprehensive system health check'
    )

    parser.add_argument(
        '--status',
        action='store_true',
        help='Show current system status'
    )

    parser.add_argument(
        '--optimize_gpu',
        action='store_true',
        help='Optimize GPU settings for better performance'
    )

    # Monitoring
    parser.add_argument(
        '--monitor',
        action='store_true',
        help='Enable real-time monitoring during execution'
    )

    # Verbose logging
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("[DEBUG] Verbose logging enabled")

    # Override trading mode if specified
    if hasattr(args, 'trading_mode') and args.trading_mode:
        os.environ['TRADING_MODE'] = args.trading_mode
        logger.info(f"[WORKFLOW] Trading mode overridden to: {args.trading_mode}")
    elif args.agent == 'backtesting' or args.agent == 'ai_training':
        # If backtesting or ai_training agent is selected and trading mode is not explicitly set,
        # force it to 'real' to ensure real data usage.
        os.environ['TRADING_MODE'] = 'real'
        logger.info(f"[WORKFLOW] Forcing trading mode to 'real' for {args.agent} agent.")

    # Initialize ConfigurationLoader after argument parsing
    config_loader_instance = ConfigurationLoader()

    # Create orchestrator
    orchestrator = TradingSystemOrchestrator(config_loader_instance)

    try:
        # System management commands
        if args.health_check:
            logger.info("[HEALTH] Running health check...")
            health_status = await orchestrator.health_check()
            print("\n" + "="*80)
            print("[HEALTH] SYSTEM HEALTH CHECK RESULTS")
            print("="*80)
            print(f"[STATUS] Overall Status: {health_status.get('overall_status', 'unknown')}")
            print(f"[TIME] Timestamp: {health_status.get('timestamp', 'unknown')}")

            if 'system_info' in health_status:
                print(f"\n[SYSTEM] System Resources:")
                for key, value in health_status['system_info'].items():
                    print(f"   • {key}: {value}")

            if 'components' in health_status:
                print(f"\n[CONFIG] Components:")
                for component, status in health_status['components'].items():
                    print(f"   • {component}: {status}")

            if health_status.get('recommendations'):
                print(f"\n[INFO] Recommendations:")
                for rec in health_status['recommendations']:
                    print(f"   • {rec}")

            return

        if args.status:
            logger.info("[STATUS] Getting system status...")
            status = await orchestrator.get_status()
            print("\n" + "="*80)
            print("[STATUS] SYSTEM STATUS")
            print("="*80)
            print(f"[TIME] Timestamp: {status.get('timestamp', 'unknown')}")
            print(f"[UPTIME] Uptime: {status.get('uptime_hours', 0):.2f} hours")
            print(f"[AGENT] Running Agents: {status.get('agent_count', 0)}")

            if status.get('running_agents'):
                print(f"[LIST] Active Agents:")
                for agent in status['running_agents']:
                    print(f"   • {agent}")

            return

        if args.optimize_gpu:
            logger.info("[CONFIG] Optimizing GPU settings...")
            success = await orchestrator.optimize_gpu()
            if success:
                print("[SUCCESS] GPU optimization completed successfully")
            else:
                print("[WARN] GPU optimization skipped or failed")
            return

        # Agent execution
        if args.agent:
            logger.info(f"[TARGET] Running {args.agent} agent...")
            success = await orchestrator.run_agent(
                args.agent,
                config_path=args.config,
                monitor=args.monitor
            )

            if success:
                print(f"[SUCCESS] {args.agent} agent completed successfully")
            else:
                print(f"[ERROR] {args.agent} agent failed")
                sys.exit(1)

            return

        # Workflow execution
        if args.workflow:
            logger.info(f"[WORKFLOW] Running {args.workflow} workflow...")
            success = await orchestrator.run_workflow(
                args.workflow,
                monitor=args.monitor
            )

            if success:
                print(f"[SUCCESS] {args.workflow} workflow completed successfully")
            else:
                print(f"[ERROR] {args.workflow} workflow failed")
                sys.exit(1)

            return

        # If no specific command, show help
        parser.print_help()

    except KeyboardInterrupt:
        logger.info("[EXIT] Trading system interrupted by user")
    except Exception as e:
        logger.error(f"[ERROR] Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    """Entry point"""
    try:
        # Ensure required directories exist
        required_dirs = ['logs', 'data/features', 'data/backtest', 'data/models']
        for dir_path in required_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)

        # Run main function
        asyncio.run(main())

    except KeyboardInterrupt:
        print("\n[EXIT] Trading system interrupted by user")
    except Exception as e:
        print(f"[ERROR] Fatal error: {e}")
        sys.exit(1)
