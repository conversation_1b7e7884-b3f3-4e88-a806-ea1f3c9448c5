"""
Configuration module for Enhanced AI Training
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any
import torch

try:
    from catboost import CatBoostRegressor
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

try:
    from pytorch_tabnet.tab_model import TabNetRegressor
    TABNET_AVAILABLE = True
except ImportError:
    TABNET_AVAILABLE = False

@dataclass
class EnhancedAITrainingConfig:
    """Enhanced Configuration for Production-Ready AI Training"""
    
    # Data Configuration
    data_dir: str = "data/backtest"
    input_pattern: str = "backtest_*.parquet"  # Pattern to match multiple files
    symbol_filter: str = None  # Filter by specific symbol (e.g., "360ONE")
    timeframe_filter: str = None  # Filter by timeframe (e.g., "3min")
    models_dir: str = "data/models/enhanced"
    registry_dir: str = "data/models/registry"
    
    # Multi-Task Learning Configuration
    prediction_tasks: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Model Configuration
    enabled_models: List[str] = field(default_factory=lambda: [
        "lightgbm", "xgboost", "catboost", "tabnet", "mlp", "sgd"
    ])
    
    # Ensemble Configuration
    ensemble_method: str = "stacking"
    stacking_cv_folds: int = 3
    blending_holdout_size: float = 0.2
    
    # Hyperparameter Optimization
    hyperopt_all_models: bool = False  # Disable for faster training
    optuna_trials: int = 10
    optuna_timeout: int = 300
    
    # Evaluation Configuration
    use_advanced_metrics: bool = True
    handle_imbalanced_data: bool = True
    
    # Feature Engineering
    feature_selection_enabled: bool = True
    max_features: int = 50
    outlier_detection_enabled: bool = True
    advanced_imputation: bool = True
    feature_columns: List[str] = field(default_factory=list)
    
    # Training Configuration
    test_size: float = 0.2
    validation_size: float = 0.2
    random_state: int = 42
    cv_folds: int = 5
    
    # Hardware Configuration
    n_jobs: int = -1
    use_gpu: bool = False
    
    def __post_init__(self):
        """Initialize default configurations"""
        
        # Check GPU availability
        if TABNET_AVAILABLE and torch.cuda.is_available():
            self.use_gpu = True
        
        # Initialize prediction tasks - only use columns that exist
        if not self.prediction_tasks:
            self.prediction_tasks = {
                "roi_prediction": {
                    "type": "regression", 
                    "target_column": "roi",
                    "weight": 1.0,
                    "metrics": ["r2_score", "rmse", "mae"]
                }
            }
        
        # Filter enabled models based on availability
        available_models = ["lightgbm", "xgboost", "mlp", "sgd"]
        if CATBOOST_AVAILABLE:
            available_models.append("catboost")
        if TABNET_AVAILABLE:
            available_models.append("tabnet")
        
        self.enabled_models = [m for m in self.enabled_models if m in available_models]