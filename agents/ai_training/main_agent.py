"""
Main AI Training Agent - Orchestrates all modules
"""

import logging
import numpy as np
import polars as pl
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from typing import Dict, Any
from numba import jit
import warnings
import os

# Comprehensive warning suppression
warnings.filterwarnings('ignore')
os.environ['PYTHONWARNINGS'] = 'ignore'

# Suppress specific library warnings
warnings.filterwarnings('ignore', category=UserWarning, module='cupy')
warnings.filterwarnings('ignore', category=UserWarning, module='lightgbm')
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=DeprecationWarning)

# Set environment variables for cleaner output
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suppress TensorFlow warnings
os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # Use first GPU only

from .config import EnhancedAITrainingConfig
from .data_handler import DataHandler
from .hyperopt_optimizer import HyperparameterOptimizer
from .model_trainer import ModelTrainer
from .ensemble_creator import EnsembleCreator
from .evaluator import ModelEvaluator
from .model_persistence import ModelPersistence
from .meta_learner import MetaLearner
from .logging_config import ProgressLogger, suppress_library_output
from .gpu_optimizer import gpu_optimizer

logger = logging.getLogger(__name__)

@jit(nopython=True)
def fast_train_test_split_indices(n_samples: int, test_size: float, random_state: int):
    """Fast JIT-compiled train/test split index generation"""
    np.random.seed(random_state)
    indices = np.arange(n_samples)
    np.random.shuffle(indices)
    split_idx = int(n_samples * (1 - test_size))
    return indices[:split_idx], indices[split_idx:]

@jit(nopython=True)
def fast_label_encode(y: np.ndarray):
    """Fast JIT-compiled label encoding"""
    unique_vals = np.unique(y)
    encoded = np.zeros(len(y), dtype=np.int32)
    for i, val in enumerate(unique_vals):
        encoded[y == val] = i
    return encoded, unique_vals

class EnhancedAITrainingAgent:
    """Main orchestrator for the enhanced AI training system"""
    
    def __init__(self, config: EnhancedAITrainingConfig = None, fast_startup: bool = True):
        self.config = config or EnhancedAITrainingConfig()
        self.fast_startup = fast_startup
        
        # Model storage
        self.models = {}
        self.ensemble_models = {}
        self.meta_models = {}
        self.online_models = {}
        
        # Training state
        self.is_trained = False
        self.training_history = []
        self.model_performance = {}
        
        # Lazy initialization flags
        self._components_initialized = False
        
        if fast_startup:
            logger.info("Enhanced AI Training Agent initialized (fast startup mode)")
        else:
            self._initialize_components()
            logger.info("Enhanced AI Training Agent initialized (full mode)")
    
    def _initialize_components(self):
        """Initialize all components (lazy loading)"""
        if self._components_initialized:
            return
            
        # Initialize modules
        self.data_handler = DataHandler(self.config)
        self.hyperopt_optimizer = HyperparameterOptimizer(self.config)
        self.model_trainer = ModelTrainer(self.config)
        self.ensemble_creator = EnsembleCreator(self.config)
        self.evaluator = ModelEvaluator(self.config)
        self.model_persistence = ModelPersistence(self.config)
        self.meta_learner = MetaLearner(self.config)
        
        self._components_initialized = True
        logger.info("AI Training components initialized")
    
    async def train_enhanced_models(self, file_path: str = None, symbol: str = None, timeframe: str = None) -> Dict[str, Any]:
        """Main training workflow with all enhancements"""
        
        logger.info("Starting enhanced AI training workflow...")
        
        # Initialize components if not done yet
        self._initialize_components()
        
        try:
            # Apply filters if provided
            if symbol:
                self.config.symbol_filter = symbol
                logger.info(f"Training on symbol: {symbol}")
            if timeframe:
                self.config.timeframe_filter = timeframe
                logger.info(f"Training on timeframe: {timeframe}")
            
            # Load data
            df = self.data_handler.load_data()
            if df is None or len(df) == 0:
                return {"error": "No data available for training"}
            
            logger.info(f"Loaded {len(df)} samples for training")
            
            # Preprocess data to get feature columns
            df_processed, feature_columns, target_columns = self.data_handler.preprocess_data(df)
            
            # Update config with discovered feature columns if not set
            if not self.config.feature_columns:
                self.config.feature_columns = feature_columns
            
            # Keep as Polars for performance - convert only when needed for sklearn
            X_pl = df_processed.select(feature_columns)
            
            training_results = {}
            task_predictions = {}
            
            # Train models for each prediction task
            for task_name, task_config in self.config.prediction_tasks.items():
                target_col = task_config["target_column"]
                
                if target_col not in df_processed.columns:
                    logger.warning(f"Target column '{target_col}' not found for task '{task_name}'")
                    continue
                
                logger.info(f"Training models for task: {task_name}")
                
                # Extract target using Polars operations
                y_pl = df_processed.select(target_col)
                
                # Handle missing targets with Polars
                valid_df = df_processed.filter(pl.col(target_col).is_not_null())
                
                if valid_df.height == 0:
                    logger.warning(f"No valid data for task: {task_name}")
                    continue
                
                # Convert to numpy only when needed for sklearn
                X_task = valid_df.select(feature_columns).to_numpy()
                y_task = valid_df.select(target_col).to_numpy().flatten()
                

                
                # Encode categorical targets
                if task_config["type"] == "classification":
                    if y_task.dtype.kind in ['U', 'S', 'O']:  # String/object types
                        le = LabelEncoder()
                        y_task = le.fit_transform(y_task)
                        self.data_handler.encoders[task_name] = le
                    else:
                        # Use fast JIT encoding for numeric categories
                        y_task_encoded, unique_vals = fast_label_encode(y_task)
                        y_task = y_task_encoded
                        # Store mapping for later use
                        le = LabelEncoder()
                        le.classes_ = unique_vals
                        self.data_handler.encoders[task_name] = le
                
                # Optimized train/test split
                if task_config["type"] == "classification":
                    # Use sklearn for stratified split in classification
                    X_train, X_test, y_train, y_test = train_test_split(
                        X_task, y_task, test_size=self.config.test_size,
                        random_state=self.config.random_state, stratify=y_task
                    )
                else:
                    # Use fast JIT split for regression
                    train_idx, test_idx = fast_train_test_split_indices(
                        len(X_task), self.config.test_size, self.config.random_state
                    )
                    X_train, X_test = X_task[train_idx], X_task[test_idx]
                    y_train, y_test = y_task[train_idx], y_task[test_idx]
                
                # Feature preparation
                X_train_processed = self.data_handler.prepare_features(
                    X_train, y_train, task_name, task_config["type"]
                )
                X_test_processed = self.data_handler.scalers[task_name].transform(X_test)
                
                # Apply imputation and feature selection if available
                if task_name in self.data_handler.imputers:
                    X_test_processed = self.data_handler.imputers[task_name].transform(X_test_processed)
                if task_name in self.data_handler.feature_selectors:
                    X_test_processed = self.data_handler.feature_selectors[task_name].transform(X_test_processed)
                
                # Hyperparameter optimization
                if self.config.hyperopt_all_models:
                    best_params = self.hyperopt_optimizer.optimize_all_models(
                        X_train_processed, y_train, task_config["type"], self.config.enabled_models
                    )
                else:
                    best_params = {model: {} for model in self.config.enabled_models}
                
                # Train individual models
                task_models = {}
                task_results = {}
                
                for model_name in self.config.enabled_models:
                    try:
                        model = self.model_trainer.create_model(
                            model_name, task_config["type"], best_params[model_name]
                        )
                        
                        # Optimized validation split
                        if task_config["type"] == "classification":
                            X_train_val, X_val, y_train_val, y_val = train_test_split(
                                X_train_processed, y_train, test_size=0.2, 
                                random_state=self.config.random_state, stratify=y_train
                            )
                        else:
                            val_idx_train, val_idx_val = fast_train_test_split_indices(
                                len(X_train_processed), 0.2, self.config.random_state
                            )
                            X_train_val = X_train_processed[val_idx_train]
                            X_val = X_train_processed[val_idx_val]
                            y_train_val = y_train[val_idx_train]
                            y_val = y_train[val_idx_val]
                        
                        trained_model = self.model_trainer.train_model(
                            model_name, model, X_train_val, y_train_val, X_val, y_val
                        )
                        
                        # Comprehensive evaluation
                        metrics = self.evaluator.evaluate_comprehensive(
                            trained_model, X_test_processed, y_test, task_config["type"]
                        )
                        
                        task_models[model_name] = trained_model
                        task_results[model_name] = metrics
                        
                        # Store predictions for meta-learning
                        predictions = trained_model.predict(X_test_processed)
                        if task_name not in task_predictions:
                            task_predictions[task_name] = predictions
                        
                        # Clean score logging
                        score = metrics.get('f1_score' if task_config['type'] == 'classification' else 'r2_score', 0)
                        logger.info(f"  {model_name} - Score: {score:.4f}")
                        
                    except Exception as e:
                        logger.error(f"Failed to train {model_name} for {task_name}: {e}")
                        continue
                
                # Create advanced ensemble with optimized splits
                if len(task_models) > 1:
                    try:
                        if task_config["type"] == "classification":
                            X_train_ens, X_val_ens, y_train_ens, y_val_ens = train_test_split(
                                X_train_processed, y_train, test_size=0.2, 
                                random_state=self.config.random_state, stratify=y_train
                            )
                        else:
                            ens_idx_train, ens_idx_val = fast_train_test_split_indices(
                                len(X_train_processed), 0.2, self.config.random_state + 1
                            )
                            X_train_ens = X_train_processed[ens_idx_train]
                            X_val_ens = X_train_processed[ens_idx_val]
                            y_train_ens = y_train[ens_idx_train]
                            y_val_ens = y_train[ens_idx_val]
                        
                        ensemble = self.ensemble_creator.create_ensemble(
                            task_models, X_train_ens, y_train_ens, X_val_ens, y_val_ens, task_config["type"]
                        )
                        
                        # Evaluate ensemble
                        ensemble_metrics = self.evaluator.evaluate_ensemble(
                            ensemble, X_test_processed, y_test, task_config["type"]
                        )
                        
                        self.ensemble_models[task_name] = ensemble
                        task_results["ensemble"] = ensemble_metrics
                        
                        logger.info(f"  Ensemble - Score: {ensemble_metrics.get('f1_score' if task_config['type'] == 'classification' else 'r2_score', 0):.4f}")
                        
                    except Exception as e:
                        logger.error(f"Failed to create ensemble for {task_name}: {e}")
                
                self.models[task_name] = task_models
                training_results[task_name] = task_results
                self.model_performance[task_name] = task_results
            
            # Train meta-learning models
            if len(task_predictions) > 1:
                logger.info("Training meta-learning models...")
                meta_results = self.meta_learner.train_meta_models(df_processed, task_predictions)
                training_results["meta_models"] = meta_results
                self.meta_models = self.meta_learner.meta_models
            
            # Mark as trained
            self.is_trained = True
            
            # Save models
            self._save_all_models()
            
            # Create training summary
            summary = {
                "status": "success",
                "timestamp": datetime.now().isoformat(),
                "tasks_trained": len(training_results),
                "models_per_task": len(self.config.enabled_models),
                "ensemble_method": self.config.ensemble_method,
                "hyperopt_enabled": self.config.hyperopt_all_models,
                "training_results": training_results
            }
            
            self.training_history.append(summary)
            
            logger.info("Enhanced AI training completed successfully!")
            return summary
            
        except Exception as e:
            logger.error(f"Enhanced AI training failed: {e}")
            return {"status": "error", "error": str(e)}
    
    def _save_all_models(self):
        """Save all models using the persistence module"""
        
        preprocessing_objects = {
            "scalers": self.data_handler.scalers,
            "encoders": self.data_handler.encoders,
            "feature_selectors": self.data_handler.feature_selectors,
            "imputers": self.data_handler.imputers
        }
        
        metadata = {
            "timestamp": datetime.now().isoformat(),
            "config": self.config.__dict__,
            "model_performance": self.model_performance,
            "training_history": self.training_history,
            "is_trained": self.is_trained
        }
        
        self.model_persistence.save_models(
            models=self.models,
            ensemble_models=self.ensemble_models,
            meta_models=self.meta_models,
            online_models=self.online_models,
            preprocessing_objects=preprocessing_objects,
            metadata=metadata
        )
    
    def load_models(self, model_name: str = "enhanced_ai_ensemble"):
        """Load models using the persistence module"""
        
        loaded_objects = self.model_persistence.load_models(model_name)
        
        # Restore state
        self.models = loaded_objects["models"]
        self.online_models = loaded_objects["online_models"]
        self.ensemble_models = loaded_objects["ensemble_models"]
        self.meta_models = loaded_objects["meta_models"]
        
        # Restore preprocessing objects
        preprocessing = loaded_objects["preprocessing"]
        self.data_handler.scalers = preprocessing.get("scalers", {})
        self.data_handler.encoders = preprocessing.get("encoders", {})
        self.data_handler.feature_selectors = preprocessing.get("feature_selectors", {})
        self.data_handler.imputers = preprocessing.get("imputers", {})
        
        # Restore metadata
        metadata = loaded_objects["metadata"]
        self.model_performance = metadata.get("model_performance", {})
        self.training_history = metadata.get("training_history", [])
        self.is_trained = metadata.get("is_trained", False)
        
        # Update meta-learner models
        self.meta_learner.meta_models = self.meta_models
    
    def predict(self, features: Dict[str, Any], task_name: str = None, 
               use_ensemble: bool = True) -> Dict[str, Any]:
        """Make predictions using trained models"""
        
        if not self.is_trained:
            return {"error": "Models not trained"}
        
        predictions = {}
        
        # Determine tasks to predict
        tasks_to_predict = [task_name] if task_name else list(self.models.keys())
        
        for task in tasks_to_predict:
            try:
                # Prepare features
                feature_array = self.data_handler.prepare_prediction_features(features, task)
                
                if feature_array is None:
                    predictions[task] = {"error": "Feature preparation failed"}
                    continue
                
                task_predictions = {}
                
                # Individual model predictions
                if task in self.models:
                    for model_name, model in self.models[task].items():
                        pred = model.predict(feature_array)
                        task_predictions[model_name] = pred[0] if len(pred) > 0 else None
                
                # Ensemble prediction
                if use_ensemble and task in self.ensemble_models:
                    ensemble_pred = self.ensemble_creator.predict_ensemble(
                        self.ensemble_models[task], feature_array
                    )
                    task_predictions["ensemble"] = ensemble_pred[0] if len(ensemble_pred) > 0 else None
                
                predictions[task] = task_predictions
                
            except Exception as e:
                logger.error(f"Prediction failed for task {task}: {e}")
                predictions[task] = {"error": str(e)}
        
        # Meta-predictions if available
        if len(predictions) > 1 and self.meta_models:
            try:
                # Create meta-features from task predictions
                meta_feature_list = []
                for task_preds in predictions.values():
                    if "ensemble" in task_preds and task_preds["ensemble"] is not None:
                        meta_feature_list.append(task_preds["ensemble"])
                
                if meta_feature_list:
                    meta_features = np.array(meta_feature_list).reshape(1, -1)
                    meta_predictions = self.meta_learner.predict_meta(meta_features)
                    predictions["meta_predictions"] = meta_predictions
                    
            except Exception as e:
                logger.error(f"Meta-prediction failed: {e}")
        
        return predictions