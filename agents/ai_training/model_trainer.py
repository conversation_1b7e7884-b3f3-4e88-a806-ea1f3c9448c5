"""
Model training module
Enhancement 8: Dynamic Class Handling for SGDClassifier
Enhanced with GPU optimization and warning suppression
"""

import logging
import numpy as np
import warnings
import lightgbm as lgb
import xgboost as xgb
from sklearn.neural_network import <PERSON><PERSON><PERSON><PERSON>ressor, MLPClassifier
from sklearn.linear_model import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SGDClassifier
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from typing import Dict, Any

# Suppress warnings
warnings.filterwarnings('ignore')

try:
    from catboost import CatBoostRegressor, CatBoostClassifier
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

try:
    from pytorch_tabnet.tab_model import TabNetRegressor, TabNetClassifier
    TABNET_AVAILABLE = True
except ImportError:
    TABNET_AVAILABLE = False

# Import GPU optimizer
try:
    from .gpu_optimizer import gpu_optimizer
    GPU_OPTIMIZER_AVAILABLE = True
except ImportError:
    GPU_OPTIMIZER_AVAILABLE = False

logger = logging.getLogger(__name__)

class ModelTrainer:
    """Handles model creation and training with dynamic class handling"""
    
    def __init__(self, config):
        self.config = config
    
    def create_model(self, model_name: str, task_type: str, params: Dict[str, Any] = None):
        """Create model with specified parameters"""

        if params is None:
            params = {}

        base_params = {"random_state": self.config.random_state}
        base_params.update(params)

        if model_name == "lightgbm":
            # Apply optimized LightGBM parameters
            lgb_params = base_params.copy()

            # Use GPU optimizer if available
            if GPU_OPTIMIZER_AVAILABLE:
                lgb_params.update(gpu_optimizer.get_optimized_lightgbm_params())
            else:
                lgb_params.update(self.config.lightgbm_params)

            if task_type == "classification":
                return lgb.LGBMClassifier(**lgb_params)
            else:
                return lgb.LGBMRegressor(**lgb_params)
        
        elif model_name == "xgboost":
            if task_type == "classification":
                return xgb.XGBClassifier(**base_params)
            else:
                return xgb.XGBRegressor(**base_params)
        
        elif model_name == "catboost" and CATBOOST_AVAILABLE:
            base_params["verbose"] = False
            if task_type == "classification":
                return CatBoostClassifier(**base_params)
            else:
                return CatBoostRegressor(**base_params)
        
        elif model_name == "tabnet" and TABNET_AVAILABLE:
            # TabNet does not accept random_state in its constructor
            tabnet_params = base_params.copy()
            tabnet_params.pop("random_state", None)
            if task_type == "classification":
                return TabNetClassifier(**tabnet_params)
            else:
                return TabNetRegressor(**tabnet_params)
        
        elif model_name == "mlp":
            if task_type == "classification":
                return MLPClassifier(**base_params, max_iter=1000)
            else:
                return MLPRegressor(**base_params, max_iter=1000)
        
        elif model_name == "sgd":
            # Enhancement 8: Dynamic class handling for SGD
            if task_type == "classification":
                return SGDClassifier(**base_params, loss='log_loss', max_iter=1000)
            else:
                return SGDRegressor(**base_params, max_iter=1000)
        
        else:
            # Fallback to RandomForest
            if task_type == "classification":
                return RandomForestClassifier(**base_params)
            else:
                return RandomForestRegressor(**base_params)
    
    def train_sgd_with_dynamic_classes(self, X: np.ndarray, y: np.ndarray, 
                                     task_type: str, params: Dict[str, Any] = None) -> Any:
        """Enhancement 8: Train SGD model with dynamic class handling"""
        
        if params is None:
            params = {}
        
        base_params = {"random_state": self.config.random_state}
        base_params.update(params)
        
        if task_type == "classification":
            # Determine unique classes dynamically
            unique_classes = np.unique(y)
            logger.info(f"SGD Classification - Detected classes: {unique_classes}")
            
            model = SGDClassifier(
                loss='log_loss',  # For probability estimates
                max_iter=1000,
                **base_params
            )
            
            # Fit with explicit classes
            model.fit(X, y)
            
            # Store classes for future reference
            model._dynamic_classes = unique_classes
            
        else:
            model = SGDRegressor(max_iter=1000, **base_params)
            model.fit(X, y)
        
        return model
    
    def train_model(self, model_name: str, model, X_train: np.ndarray, y_train: np.ndarray,
                   X_val: np.ndarray = None, y_val: np.ndarray = None):
        """Train model with appropriate parameters"""

        if model_name == "sgd":
            # Use dynamic class handling for SGD
            task_type = "classification" if hasattr(model, 'classes_') or isinstance(model, SGDClassifier) else "regression"
            return self.train_sgd_with_dynamic_classes(X_train, y_train, task_type)

        elif model_name in ["lightgbm", "xgboost", "catboost"] and X_val is not None and y_val is not None:
            # Tree-based models with early stopping and optimized parameters
            if hasattr(model, 'fit'):
                if model_name == "lightgbm":
                    # Set force_col_wise to suppress warnings and optimize performance
                    if hasattr(model, 'set_params'):
                        model.set_params(force_col_wise=True, verbose=-1)

                    # Train without early stopping for complete training
                    model.fit(
                        X_train, y_train,
                        eval_set=[(X_val, y_val)],
                        callbacks=[lgb.log_evaluation(0)]  # Only logging, no early stopping
                    )
                elif model_name == "xgboost":
                    # XGBoost parameter fix for newer versions
                    try:
                        # Try new parameter name first
                        model.fit(
                            X_train, y_train,
                            eval_set=[(X_val, y_val)],
                            verbose=False
                        )
                    except Exception as e:
                        # Fallback to basic training without early stopping
                        logger.warning(f"XGBoost early stopping failed, using basic training: {e}")
                        model.fit(X_train, y_train, verbose=False)
                elif model_name == "catboost":
                    # Train without early stopping for complete training
                    model.fit(
                        X_train, y_train,
                        eval_set=(X_val, y_val),
                        verbose=False
                    )
        
        elif model_name == "tabnet" and X_val is not None and y_val is not None:
            # TabNet with validation
            # Ensure y_train and y_val are numpy arrays
            y_train_np = y_train.to_numpy() if hasattr(y_train, 'to_numpy') else y_train
            y_val_np = y_val.to_numpy() if hasattr(y_val, 'to_numpy') else y_val

            # Determine if it's a regression task to reshape y
            is_regression = isinstance(model, TabNetRegressor)

            y_train_processed = y_train_np
            y_val_processed = y_val_np

            if is_regression:
                # For regression, ensure y is 2D (n_samples, 1)
                if y_train_np.ndim == 1:
                    y_train_processed = y_train_np.reshape(-1, 1)
                elif y_train_np.ndim == 2 and y_train_np.shape[1] != 1:
                    logger.warning(f"TabNetRegressor received 2D y_train with shape {y_train_np.shape}. "
                                   "Expected (n_samples,) or (n_samples, 1) for single-output regression. "
                                   "Proceeding as is, but check data if unexpected multi-output.")
                
                if y_val_np.ndim == 1:
                    y_val_processed = y_val_np.reshape(-1, 1)
                elif y_val_np.ndim == 2 and y_val_np.shape[1] != 1:
                    logger.warning(f"TabNetRegressor received 2D y_val with shape {y_val_np.shape}. "
                                   "Expected (n_samples,) or (n_samples, 1) for single-output regression. "
                                   "Proceeding as is, but check data if unexpected multi-output.")
            else: # Classification
                # For classification, ensure y is 1D (n_samples,)
                if y_train_np.ndim == 2:
                    if y_train_np.shape[1] == 1:
                        y_train_processed = y_train_np.squeeze()
                    elif y_train_np.shape[0] == y_train_np.shape[1]:
                        # Handle the (N, N) case - likely a matrix instead of labels
                        logger.error(f"TabNetClassifier received square matrix y_train with shape {y_train_np.shape}. "
                                   "This appears to be a matrix rather than target labels. Check data preprocessing.")
                        raise ValueError(f"Invalid target data: received square matrix of shape {y_train_np.shape} "
                                       "instead of 1D labels for classification.")
                    else:
                        raise ValueError(f"TabNetClassifier received 2D y_train with shape {y_train_np.shape}. "
                                       "Expected 1D array (n_samples,) or 2D array (n_samples, 1) for single-output classification.")
                
                if y_val_np.ndim == 2:
                    if y_val_np.shape[1] == 1:
                        y_val_processed = y_val_np.squeeze()
                    elif y_val_np.shape[0] == y_val_np.shape[1]:
                        logger.error(f"TabNetClassifier received square matrix y_val with shape {y_val_np.shape}. "
                                   "This appears to be a matrix rather than target labels. Check data preprocessing.")
                        raise ValueError(f"Invalid target data: received square matrix of shape {y_val_np.shape} "
                                       "instead of 1D labels for classification.")
                    else:
                        raise ValueError(f"TabNetClassifier received 2D y_val with shape {y_val_np.shape}. "
                                       "Expected 1D array (n_samples,) or 2D array (n_samples, 1) for single-output classification.")

            # TabNet training WITHOUT early stopping for complete training
            train_params = {
                'max_epochs': 200,  # Complete training - NO early stopping
                'eval_metric': ['mse'] if is_regression else ['accuracy'],
                'drop_last': False,
                'batch_size': min(1024, len(X_train) // 10)
            }

            # Apply GPU optimizer parameters but FORCE no early stopping
            if GPU_OPTIMIZER_AVAILABLE:
                tabnet_params = gpu_optimizer.get_optimized_tabnet_params()
                train_params.update({
                    'max_epochs': 200,  # Force complete training
                    'batch_size': tabnet_params['batch_size']
                })

            # Explicitly remove any early stopping parameters
            train_params.pop('patience', None)
            train_params.pop('early_stopping_rounds', None)

            # Train without early stopping - NO PATIENCE PARAMETER
            model.fit(
                X_train.astype(np.float32), y_train_processed.astype(np.float32),
                eval_set=[(X_val.astype(np.float32), y_val_processed.astype(np.float32))],
                max_epochs=200,  # Explicit parameter to override any defaults
                batch_size=train_params['batch_size'],
                eval_metric=train_params['eval_metric'],
                drop_last=train_params['drop_last']
                # NO patience parameter = NO early stopping
            )
        
        else:
            # Standard sklearn models
            model.fit(X_train, y_train)
        
        return model
