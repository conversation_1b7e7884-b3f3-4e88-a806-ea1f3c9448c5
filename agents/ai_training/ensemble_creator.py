"""
Ensemble creation module
Enhancement 2: Advanced Ensemble Methods
"""

import logging
import numpy as np
from sklearn.ensemble import VotingRegressor, VotingClassifier, StackingRegressor, StackingClassifier
from sklearn.linear_model import Ridge, LogisticRegression
from sklearn.metrics import f1_score, r2_score
from typing import Dict, Any

logger = logging.getLogger(__name__)

class EnsembleCreator:
    """Creates advanced ensemble models using stacking, blending, and weighted voting"""
    
    def __init__(self, config):
        self.config = config
    
    def create_ensemble(self, models: Dict[str, Any], X_train: np.ndarray, 
                       y_train: np.ndarray, X_val: np.ndarray, 
                       y_val: np.ndarray, task_type: str) -> Any:
        """Create advanced ensemble using stacking or blending"""
        
        if self.config.ensemble_method == "stacking":
            return self._create_stacking_ensemble(models, X_train, y_train, task_type)
        elif self.config.ensemble_method == "blending":
            return self._create_blending_ensemble(models, X_train, y_train, X_val, y_val, task_type)
        elif self.config.ensemble_method == "weighted":
            return self._create_weighted_ensemble(models, X_val, y_val, task_type)
        else:
            return self._create_voting_ensemble(models, task_type)
    
    def _create_stacking_ensemble(self, models: Dict[str, Any], X_train: np.ndarray, 
                                 y_train: np.ndarray, task_type: str) -> Any:
        """Create stacking ensemble with meta-learner"""
        
        # Filter out TabNet models as they're not fully sklearn compatible
        sklearn_models = {}
        for name, model in models.items():
            if 'TabNet' not in str(type(model)):
                sklearn_models[name] = model
        
        if len(sklearn_models) < 2:
            logger.warning("Not enough sklearn-compatible models for stacking, using weighted ensemble")
            return self._create_weighted_ensemble(models, X_train, y_train, task_type)
        
        # Ensure all models have the correct _estimator_type
        processed_estimators = []
        for name, model in sklearn_models.items():
            if not hasattr(model, '_estimator_type'):
                if task_type == "classification":
                    model._estimator_type = "classifier"
                else:
                    model._estimator_type = "regressor"
            processed_estimators.append((name, model))

        if task_type == "classification":
            meta_classifier = LogisticRegression(random_state=self.config.random_state)
            ensemble = StackingClassifier(
                estimators=processed_estimators,
                final_estimator=meta_classifier,
                cv=self.config.stacking_cv_folds,
                n_jobs=self.config.n_jobs
            )
        else:
            meta_regressor = Ridge(random_state=self.config.random_state)
            ensemble = StackingRegressor(
                estimators=processed_estimators,
                final_estimator=meta_regressor,
                cv=self.config.stacking_cv_folds,
                n_jobs=self.config.n_jobs
            )
        
        ensemble.fit(X_train, y_train)
        return ensemble
    
    def _create_blending_ensemble(self, models: Dict[str, Any], X_train: np.ndarray,
                                 y_train: np.ndarray, X_val: np.ndarray, 
                                 y_val: np.ndarray, task_type: str) -> Dict[str, Any]:
        """Create blending ensemble"""
        
        # Train base models on training set
        base_predictions = {}
        for name, model in models.items():
            model.fit(X_train, y_train)
            base_predictions[name] = model.predict(X_val)
        
        # Create meta-features from base predictions
        meta_X = np.column_stack(list(base_predictions.values()))
        
        # Train meta-learner
        if task_type == "classification":
            meta_learner = LogisticRegression(random_state=self.config.random_state)
        else:
            meta_learner = Ridge(random_state=self.config.random_state)
        
        meta_learner.fit(meta_X, y_val)
        
        return {
            "base_models": models,
            "meta_learner": meta_learner,
            "type": "blending"
        }
    
    def _create_weighted_ensemble(self, models: Dict[str, Any], X_val: np.ndarray,
                                 y_val: np.ndarray, task_type: str) -> Dict[str, Any]:
        """Create weighted ensemble based on validation performance"""
        
        weights = {}
        total_weight = 0
        
        for name, model in models.items():
            predictions = model.predict(X_val)
            
            if task_type == "classification":
                score = f1_score(y_val, predictions, average='weighted')
            else:
                score = r2_score(y_val, predictions)
            
            weight = max(0, score)  # Ensure non-negative weights
            weights[name] = weight
            total_weight += weight
        
        # Normalize weights
        if total_weight > 0:
            weights = {k: v/total_weight for k, v in weights.items()}
        else:
            weights = {k: 1/len(models) for k in models.keys()}
        
        return {
            "models": models,
            "weights": weights,
            "type": "weighted"
        }
    
    def _create_voting_ensemble(self, models: Dict[str, Any], task_type: str) -> Any:
        """Create voting ensemble"""
        
        estimators = [(name, model) for name, model in models.items()]
        
        if task_type == "classification":
            return VotingClassifier(estimators=estimators, voting='soft')
        else:
            return VotingRegressor(estimators=estimators)
    
    def predict_ensemble(self, ensemble, X: np.ndarray) -> np.ndarray:
        """Make predictions with ensemble model"""
        
        if isinstance(ensemble, dict):
            if ensemble["type"] == "blending":
                # Blending ensemble prediction
                base_predictions = []
                for name, model in ensemble["base_models"].items():
                    pred = model.predict(X)
                    base_predictions.append(pred)
                
                meta_X = np.column_stack(base_predictions)
                return ensemble["meta_learner"].predict(meta_X)
            
            elif ensemble["type"] == "weighted":
                # Weighted ensemble prediction
                weighted_preds = []
                for name, model in ensemble["models"].items():
                    pred = model.predict(X)
                    weight = ensemble["weights"][name]
                    weighted_preds.append(pred * weight)
                
                return np.sum(weighted_preds, axis=0)
        
        else:
            # Standard sklearn ensemble
            return ensemble.predict(X)
