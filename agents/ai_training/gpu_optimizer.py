"""
GPU Optimization and Warning Suppression for AI Training
Handles CuPy conflicts, GPU memory management, and performance optimization
"""

import os
import logging
import warnings
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class GPUOptimizer:
    """Handles GPU optimization and warning suppression"""
    
    def __init__(self):
        self.gpu_available = False
        self.cupy_available = False
        self.cuda_version = None
        self._setup_environment()
    
    def _setup_environment(self):
        """Setup optimal environment variables and suppress warnings"""
        
        # Suppress all warnings at environment level
        os.environ['PYTHONWARNINGS'] = 'ignore'
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
        
        # CUDA optimization
        os.environ['CUDA_LAUNCH_BLOCKING'] = '0'  # Async CUDA operations
        os.environ['CUDA_CACHE_DISABLE'] = '0'    # Enable CUDA cache
        
        # CuPy optimization - resolve conflicts
        self._resolve_cupy_conflicts()
        
        # Memory optimization
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
        
        # Suppress specific warnings
        warnings.filterwarnings('ignore', category=UserWarning, module='cupy')
        warnings.filterwarnings('ignore', message='.*CuPy.*')
        warnings.filterwarnings('ignore', message='.*multiple CuPy packages.*')
        
        logger.info("[GPU] Environment optimized for training")
    
    def _resolve_cupy_conflicts(self):
        """Resolve CuPy package conflicts"""
        try:
            # Try to import cupy and detect version conflicts
            import cupy as cp
            self.cupy_available = True
            
            # Check CUDA version
            try:
                self.cuda_version = cp.cuda.runtime.runtimeGetVersion()
                logger.info(f"[GPU] CuPy detected with CUDA {self.cuda_version}")
            except Exception:
                logger.warning("[GPU] CuPy available but CUDA version detection failed")
                
        except ImportError:
            logger.info("[GPU] CuPy not available, using CPU-only mode")
        except Exception as e:
            logger.warning(f"[GPU] CuPy conflict detected: {e}")
            # Suppress the specific warning
            warnings.filterwarnings('ignore', message='.*CuPy may not function correctly.*')
    
    def get_optimized_lightgbm_params(self) -> Dict[str, Any]:
        """Get optimized LightGBM parameters"""
        params = {
            'verbose': -1,
            'force_col_wise': True,  # Suppress threading warnings
            'num_threads': os.cpu_count() or 4,
            'early_stopping_rounds': 50,
            'feature_pre_filter': False  # Disable for stability
        }
        
        # GPU optimization if available
        if self._check_gpu_availability():
            params.update({
                'device': 'gpu',
                'gpu_platform_id': 0,
                'gpu_device_id': 0,
                'gpu_use_dp': False,  # Single precision for speed
                'max_bin': 255
            })
            logger.info("[GPU] LightGBM GPU parameters configured")
        else:
            params['device'] = 'cpu'
            logger.info("[GPU] LightGBM CPU parameters configured")
        
        return params
    
    def get_optimized_tabnet_params(self) -> Dict[str, Any]:
        """Get optimized TabNet parameters"""
        params = {
            'patience': 15,
            'max_epochs': 100,
            'batch_size': 1024,
            'virtual_batch_size': 128,
            'n_d': 8,
            'n_a': 8,
            'n_steps': 3,
            'gamma': 1.3,
            'lambda_sparse': 1e-3,
            'optimizer_fn': 'torch.optim.Adam',  # Correct parameter name
            'optimizer_params': {'lr': 0.02},
            'scheduler_fn': 'torch.optim.lr_scheduler.StepLR',
            'scheduler_params': {'step_size': 10, 'gamma': 0.9},
            'mask_type': 'sparsemax'
        }
        
        # GPU optimization if available
        if self._check_gpu_availability():
            params.update({
                'device': 'cuda',
                'mixed_precision': True
            })
            logger.info("[GPU] TabNet GPU parameters configured")
        else:
            params['device'] = 'cpu'
            logger.info("[GPU] TabNet CPU parameters configured")
        
        return params
    
    def _check_gpu_availability(self) -> bool:
        """Check if GPU is available and working"""
        if self.gpu_available:
            return True
            
        try:
            import torch
            if torch.cuda.is_available():
                self.gpu_available = True
                logger.info(f"[GPU] CUDA available with {torch.cuda.device_count()} device(s)")
                return True
        except ImportError:
            pass
        
        try:
            import cupy as cp
            if cp.cuda.is_available():
                self.gpu_available = True
                logger.info("[GPU] CuPy CUDA available")
                return True
        except (ImportError, Exception):
            pass
        
        logger.info("[GPU] No GPU acceleration available, using CPU")
        return False
    
    def optimize_memory(self):
        """Optimize GPU memory usage"""
        if not self.gpu_available:
            return
        
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                logger.info("[GPU] PyTorch GPU cache cleared")
        except ImportError:
            pass
        
        try:
            import cupy as cp
            if cp.cuda.is_available():
                cp.get_default_memory_pool().free_all_blocks()
                logger.info("[GPU] CuPy memory pool cleared")
        except (ImportError, Exception):
            pass
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information for debugging"""
        info = {
            'gpu_available': self.gpu_available,
            'cupy_available': self.cupy_available,
            'cuda_version': self.cuda_version,
            'cpu_count': os.cpu_count()
        }
        
        try:
            import torch
            info['pytorch_cuda'] = torch.cuda.is_available()
            if torch.cuda.is_available():
                info['pytorch_cuda_devices'] = torch.cuda.device_count()
        except ImportError:
            info['pytorch_cuda'] = False
        
        return info

# Global instance
gpu_optimizer = GPUOptimizer()
